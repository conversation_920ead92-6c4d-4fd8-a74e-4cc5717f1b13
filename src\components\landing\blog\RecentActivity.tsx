"use client";

import React, { useEffect, useState } from "react";
import BlogCard from "./BlogCard";
import { cn, stripHtml } from "@/lib/utils";
import { useBlog } from "@/contexts/BlogContext";
import BlogCardSkeleton from "@/components/common/BlogCardSkeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const RecentActivity = () => {
  const {
    recentBlogs,
    recentBlogsLoading,
    recentBlogsError,
    fetchBlogs,
  } = useBlog();

  const [sortBy, setSortBy] = useState("publishedAt");

  useEffect(() => {
    fetchBlogs({
      sortBy: sortBy as
        | "createdAt"
        | "publishedAt"
        | "updatedAt"
        | "title"
        | "viewCount",
      sortOrder: sortBy === "publishedAt" ? "desc" : "asc",
    });
  }, [sortBy, fetchBlogs]);

  const handleSortChange = (value: string) => {
    setSortBy(value);
  };

  // Split blogs into first row (2 blogs) and remaining blogs
  const firstRowBlogs = recentBlogs.slice(0, 2);
  const remainingBlogs = recentBlogs.slice(2);

  return (
    <div className="w-full">
      <div className="mb-5 flex items-center justify-between">
        <h2 className="text-3xl font-bold text-accent lg:text-5xl">
          Blogs
        </h2>
        {recentBlogsLoading ? (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Sort by:</span>
            <div className="h-10 w-[120px] animate-pulse rounded bg-gray-200"></div>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Sort by:</span>
            <Select value={sortBy} onValueChange={handleSortChange}>
              <SelectTrigger className="w-[120px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem className="focus:text-white" value="publishedAt">
                  Newest
                </SelectItem>
                <SelectItem className="focus:text-white" value="createdAt">
                  Oldest
                </SelectItem>
                <SelectItem className="focus:text-white" value="viewCount">
                  Popular
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      {recentBlogsLoading ? (
        <>
          {/* First Row: Medium + Large Cards */}
          <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-5">
            <div className="md:col-span-2">
              <BlogCardSkeleton isMedium />
            </div>
            <div className="md:col-span-3">
              <BlogCardSkeleton isLarge />
            </div>
          </div>

          {/* Remaining Cards: 3-Column Grid */}
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <BlogCardSkeleton isSmallMedium />
            <BlogCardSkeleton isSmallMedium />
            <BlogCardSkeleton isSmallMedium />
            <BlogCardSkeleton isSmallMedium />
          </div>
        </>
      ) : recentBlogsError ? (
        <div className="py-32 text-center">
          <p className="text-red-500">{recentBlogsError}</p>
        </div>
      ) : recentBlogs.length === 0 ? (
        <div className="py-32 text-center">
          <p className="text-gray-500">No Data</p>
        </div>
      ) : (
        <>
          {/* First Row: Medium + Large Cards */}
          <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-5">
            {firstRowBlogs.map((post, index) => {
              const textContent = stripHtml(post.content);
              const excerpt =
                textContent.length > 150
                  ? textContent.substring(0, 150).trim() + "..."
                  : textContent;

              return (
                <div
                  key={post._id}
                  className={cn(
                    index === 1 ? "md:col-span-3" : "md:col-span-2",
                  )}
                >
                  <BlogCard
                    id={parseInt(post._id.slice(-6), 16) || 0}
                    title={post.title}
                    excerpt={excerpt}
                    image={
                      post.imageUrl || "/assets/images/free-nature-images.jpg"
                    }
                    tags={post.tags || []}
                    isLarge={index === 1}
                    isMedium={index === 0}
                    slug={post.slug || post._id}
                  />
                </div>
              );
            })}
          </div>

          {/* Remaining Cards: 3-Column Grid */}
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {remainingBlogs.map((post) => {
              const textContent = stripHtml(post.content);
              const excerpt =
                textContent.length > 150
                  ? textContent.substring(0, 150).trim() + "..."
                  : textContent;

              return (
                <div key={post._id}>
                  <BlogCard
                    id={parseInt(post._id.slice(-6), 16) || 0}
                    title={post.title}
                    excerpt={excerpt}
                    image={
                      post.imageUrl || "/assets/images/free-nature-images.jpg"
                    }
                    tags={post.tags || []}
                    isSmallMedium={true}
                    slug={post.slug || post._id}
                  />
                </div>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
};

export default RecentActivity;
