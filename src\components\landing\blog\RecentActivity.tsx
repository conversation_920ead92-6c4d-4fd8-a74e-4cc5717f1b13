"use client";

import React, { useEffect, useState, useCallback } from "react";
import BlogCard from "./BlogCard";
import { cn, stripHtml } from "@/lib/utils";
import { useBlog } from "@/contexts/BlogContext";
import BlogCardSkeleton from "@/components/common/BlogCardSkeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Search, X } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

const RecentActivity = () => {
  const {
    blogs,
    blogsLoading,
    blogsError,
    blogsPagination,
    fetchBlogs,
  } = useBlog();

  console.log(blogsPagination, "_____blogs")

  const [sortBy, setSortBy] = useState("publishedAt");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [debouncedSearch, setDebouncedSearch] = useState("");

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch blogs when dependencies change
  useEffect(() => {
    fetchBlogs({
      sortBy: sortBy as
        | "createdAt"
        | "publishedAt"
        | "updatedAt"
        | "title"
        | "viewCount",
      sortOrder: sortBy === "publishedAt" ? "desc" : "asc",
      search: debouncedSearch || undefined,
      page: currentPage,
      limit: 8,
    });
  }, [sortBy, debouncedSearch, currentPage, fetchBlogs]);

  // Reset to first page when search or sort changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearch, sortBy]);

  const handleSortChange = (value: string) => {
    setSortBy(value);
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Generate page numbers for pagination
  const generatePageNumbers = useCallback(() => {
    if (!blogsPagination) return [];

    const { currentPage, totalPages } = blogsPagination;
    const pages: (number | string)[] = [];

    if (totalPages <= 7) {
      // Show all pages if 7 or fewer
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (currentPage > 3) {
        pages.push('...');
      }

      // Show pages around current page
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (currentPage < totalPages - 2) {
        pages.push('...');
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  }, [blogsPagination]);

  // Split blogs into first row (2 blogs) and remaining blogs
  const firstRowBlogs = blogs.slice(0, 2);
  const remainingBlogs = blogs.slice(2);

  return (
    <div className="w-full">
      <div className="mb-5 flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold text-accent lg:text-5xl">
            Blogs
          </h2>
          {blogsLoading ? (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Sort by:</span>
              <div className="h-10 w-[120px] animate-pulse rounded bg-gray-200"></div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Sort by:</span>
              <Select value={sortBy} onValueChange={handleSortChange}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem className="focus:text-white" value="publishedAt">
                    Newest
                  </SelectItem>
                  <SelectItem className="focus:text-white" value="createdAt">
                    Oldest
                  </SelectItem>
                  <SelectItem className="focus:text-white" value="viewCount">
                    Popular
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {/* Search Input */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <Input
            type="text"
            placeholder="Search blogs..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 pr-10"
            disabled={blogsLoading}
          />
          {searchQuery && !blogsLoading && (
            <button
              onClick={() => handleSearchChange("")}
              className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
          {blogsLoading && searchQuery && (
            <div className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600"></div>
            </div>
          )}
        </div>

        {/* Search Results Info */}
        {debouncedSearch && !blogsLoading && blogsPagination && (
          <div className="text-sm text-gray-600">
            {blogsPagination.totalItems > 0
              ? `Found ${blogsPagination.totalItems} result${blogsPagination.totalItems === 1 ? '' : 's'} for "${debouncedSearch}"`
              : `No results found for "${debouncedSearch}"`
            }
          </div>
        )}
      </div>

      {blogsLoading ? (
        <>
          {/* First Row: Medium + Large Cards */}
          <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-5">
            <div className="md:col-span-2">
              <BlogCardSkeleton isMedium />
            </div>
            <div className="md:col-span-3">
              <BlogCardSkeleton isLarge />
            </div>
          </div>

          {/* Remaining Cards: 3-Column Grid */}
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <BlogCardSkeleton isSmallMedium />
            <BlogCardSkeleton isSmallMedium />
            <BlogCardSkeleton isSmallMedium />
            <BlogCardSkeleton isSmallMedium />
          </div>
        </>
      ) : blogsError ? (
        <div className="py-32 text-center">
          <p className="text-red-500">{blogsError}</p>
        </div>
      ) : blogs.length === 0 ? (
        <div className="py-32 text-center">
          <p className="text-gray-500">
            {debouncedSearch
              ? `No blogs found for "${debouncedSearch}"`
              : "No blogs available"
            }
          </p>
          {debouncedSearch && (
            <button
              onClick={() => handleSearchChange("")}
              className="mt-2 text-sm text-blue-600 hover:text-blue-800 underline"
            >
              Clear search
            </button>
          )}
        </div>
      ) : (
        <>
          {/* First Row: Medium + Large Cards */}
          <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-5">
            {firstRowBlogs.map((post, index) => {
              const textContent = stripHtml(post.content);
              const excerpt =
                textContent.length > 150
                  ? textContent.substring(0, 150).trim() + "..."
                  : textContent;

              return (
                <div
                  key={post._id}
                  className={cn(
                    index === 1 ? "md:col-span-3" : "md:col-span-2",
                  )}
                >
                  <BlogCard
                    id={parseInt(post._id.slice(-6), 16) || 0}
                    title={post.title}
                    excerpt={excerpt}
                    image={
                      post.imageUrl || "/assets/images/free-nature-images.jpg"
                    }
                    tags={post.tags || []}
                    isLarge={index === 1}
                    isMedium={index === 0}
                    slug={post.slug || post._id}
                  />
                </div>
              );
            })}
          </div>

          {/* Remaining Cards: 3-Column Grid */}
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {remainingBlogs.map((post) => {
              const textContent = stripHtml(post.content);
              const excerpt =
                textContent.length > 150
                  ? textContent.substring(0, 150).trim() + "..."
                  : textContent;

              return (
                <div key={post._id}>
                  <BlogCard
                    id={parseInt(post._id.slice(-6), 16) || 0}
                    title={post.title}
                    excerpt={excerpt}
                    image={
                      post.imageUrl || "/assets/images/free-nature-images.jpg"
                    }
                    tags={post.tags || []}
                    isSmallMedium={true}
                    slug={post.slug || post._id}
                  />
                </div>
              );
            })}
          </div>

          {/* Pagination */}
          {blogsPagination && blogsPagination.totalPages > 1 && (
            <div className="mt-12 flex justify-center">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={!blogsPagination.hasPrev}
                      className={cn(
                        !blogsPagination.hasPrev && "opacity-50 cursor-not-allowed"
                      )}
                    />
                  </PaginationItem>

                  {generatePageNumbers().map((page, index) => (
                    <PaginationItem key={index}>
                      {page === '...' ? (
                        <PaginationEllipsis />
                      ) : (
                        <PaginationLink
                          onClick={() => handlePageChange(page as number)}
                          isActive={page === currentPage}
                        >
                          {page}
                        </PaginationLink>
                      )}
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={!blogsPagination.hasNext}
                      className={cn(
                        !blogsPagination.hasNext && "opacity-50 cursor-not-allowed"
                      )}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default RecentActivity;
